# Flask 环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production

# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost/model_registry

# 管理员配置
ADMIN_PASSWORD=your-admin-password

# API 配置
API_ENABLED=true
API_VERSION=v1

# 安全设置
MAX_LOGIN_ATTEMPTS=5
LOGIN_ATTEMPT_TIMEOUT=300

# 分页配置
ITEMS_PER_PAGE=20

# 日志配置
LOG_LEVEL=INFO

# 会话配置
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# 数据库连接池配置
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=20
DB_POOL_RECYCLE=-1
DB_POOL_PRE_PING=true

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=100
RATE_LIMIT_WINDOW=3600

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# 邮件配置（如果需要）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 监控配置
MONITORING_ENABLED=false
METRICS_ENDPOINT=/metrics

# 备份配置
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400  # 24小时
BACKUP_RETENTION=7     # 保留7天
