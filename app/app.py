# app.py
from flask import Flask, render_template
from models import db
from config import get_config
from utils.logging_config import setup_logging
from utils.error_handlers import register_error_handlers, handle_application_error
from blueprints.auth import auth_bp
from blueprints.main import main_bp
from blueprints.api import api_bp
import os

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    config_class = get_config()
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)

    # 设置日志
    setup_logging(app)

    # 注册错误处理器
    register_error_handlers(app)
    handle_application_error(app)

    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp)

    # 创建数据库表
    with app.app_context():
        db.create_all()

    return app

# 创建应用实例
app = create_app()



if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

# ======== 模型管理 ========
@app.route('/models')
@login_required
def list_models():
    models = AIModel.query.all()
    return render_template('models/list.html', models=models)

@app.route('/models/add', methods=['GET', 'POST'])
@login_required
def add_model():
    platforms = Platform.query.all()

    if request.method == 'POST':
        display_name = request.form['display_name']
        internal_name = request.form['internal_name']
        api_endpoint = request.form['api_endpoint']
        platform_id = request.form['platform_id']
        input_token_price = Decimal(request.form['input_token_price'])
        output_token_price = Decimal(request.form['output_token_price'])
        input_picture_price = Decimal(request.form['input_picture_price'])
        is_visible_model = 'is_visible_model' in request.form
        free = 'free' in request.form
        high_price = 'high_price' in request.form

        if AIModel.query.filter_by(display_name=display_name).first():
            flash('模型名称已存在', 'danger')
            return redirect(url_for('add_model'))

        model = AIModel(
            display_name=display_name,
            internal_name=internal_name,
            api_endpoint=api_endpoint,
            platform_id=platform_id,
            input_token_price=input_token_price,
            output_token_price=output_token_price,
            input_picture_price=input_picture_price,
            is_visible_model=is_visible_model,
            free=free,
            high_price=high_price
        )

        db.session.add(model)
        db.session.commit()

        flash('模型添加成功', 'success')
        return redirect(url_for('list_models'))

    return render_template('models/add.html', platforms=platforms)

@app.route('/models/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_model(id):
    model = AIModel.query.get_or_404(id)
    platforms = Platform.query.all()

    if request.method == 'POST':
        display_name = request.form['display_name']
        internal_name = request.form['internal_name']
        api_endpoint = request.form['api_endpoint']
        platform_id = request.form['platform_id']
        input_token_price = Decimal(request.form['input_token_price'])
        output_token_price = Decimal(request.form['output_token_price'])
        input_picture_price = Decimal(request.form['input_picture_price'])
        is_visible_model = 'is_visible_model' in request.form
        free = 'free' in request.form
        high_price = 'high_price' in request.form

        existing = AIModel.query.filter_by(display_name=display_name).first()
        if existing and existing.id != id:
            flash('模型名称已存在', 'danger')
            return redirect(url_for('edit_model', id=id))

        model.display_name = display_name
        model.internal_name = internal_name
        model.api_endpoint = api_endpoint
        model.platform_id = platform_id
        model.input_token_price = input_token_price
        model.output_token_price = output_token_price
        model.input_picture_price = input_picture_price
        model.is_visible_model = is_visible_model
        model.free = free
        model.high_price = high_price

        db.session.commit()

        flash('模型更新成功', 'success')
        return redirect(url_for('list_models'))

    return render_template('models/edit.html', model=model, platforms=platforms)

@app.route('/models/delete/<int:id>', methods=['POST'])
@login_required
def delete_model(id):
    model = AIModel.query.get_or_404(id)

    # 检查是否有关联的应用模型
    if model.app_models:
        flash('无法删除模型，请先删除关联的应用模型', 'danger')
        return redirect(url_for('list_models'))

    db.session.delete(model)
    db.session.commit()

    flash('模型删除成功', 'success')
    return redirect(url_for('list_models'))

# ======== 应用管理 ========
@app.route('/applications')
@login_required
def list_applications():
    applications = Application.query.all()
    return render_template('applications/list.html', applications=applications)

@app.route('/applications/add', methods=['GET', 'POST'])
@login_required
def add_application():
    if request.method == 'POST':
        name = request.form['name']
        description = request.form['description']
        api_key = secrets.token_hex(16)  # 自动生成API密钥

        if Application.query.filter_by(name=name).first():
            flash('应用名称已存在', 'danger')
            return redirect(url_for('add_application'))

        application = Application(name=name, description=description, api_key=api_key)
        db.session.add(application)
        db.session.commit()

        flash('应用添加成功', 'success')
        return redirect(url_for('list_applications'))

    return render_template('applications/add.html')

@app.route('/applications/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_application(id):
    application = Application.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form['name']
        description = request.form['description']

        existing = Application.query.filter_by(name=name).first()
        if existing and existing.id != id:
            flash('应用名称已存在', 'danger')
            return redirect(url_for('edit_application', id=id))

        application.name = name
        application.description = description
        db.session.commit()

        flash('应用更新成功', 'success')
        return redirect(url_for('list_applications'))

    return render_template('applications/edit.html', application=application)

@app.route('/applications/delete/<int:id>', methods=['POST'])
@login_required
def delete_application(id):
    application = Application.query.get_or_404(id)

    # 删除应用会自动删除关联的应用模型（因为设置了cascade）
    db.session.delete(application)
    db.session.commit()

    flash('应用删除成功', 'success')
    return redirect(url_for('list_applications'))

# ======== 应用模型管理 ========
@app.route('/applications/<int:id>/models', methods=['GET', 'POST'])
@login_required
def manage_app_models(id):
    application = Application.query.get_or_404(id)
    all_models = AIModel.query.all()

    # 获取应用当前关联的模型ID列表
    current_model_ids = [am.model_id for am in AppModel.query.filter_by(application_id=id).all()]

    # 获取应用当前的默认模型ID
    default_model_id = None
    default_app_model = AppModel.query.filter_by(application_id=id, is_default=True).first()
    if default_app_model:
        default_model_id = default_app_model.model_id

    if request.method == 'POST':
        # 获取提交的模型ID列表
        selected_model_ids = request.form.getlist('model_ids')
        new_default_model_id = request.form.get('default_model_id')

        # 删除不再关联的模型
        AppModel.query.filter(
            AppModel.application_id == id, 
            ~AppModel.model_id.in_(selected_model_ids) if selected_model_ids else True
        ).delete(synchronize_session=False)

        # 添加新关联的模型
        for model_id in selected_model_ids:
            model_id = int(model_id)
            # 检查是否已存在
            existing = AppModel.query.filter_by(
                application_id=id, 
                model_id=model_id
            ).first()

            if not existing:
                # 创建新关联
                app_model = AppModel(
                    application_id=id,
                    model_id=model_id,
                    is_default=(model_id == int(new_default_model_id) if new_default_model_id else False)
                )
                db.session.add(app_model)
            elif model_id == int(new_default_model_id) and not existing.is_default:
                # 更新默认状态
                existing.is_default = True
            elif existing.is_default and model_id != int(new_default_model_id):
                # 取消默认状态
                existing.is_default = False

        db.session.commit()
        flash('应用模型关联已更新', 'success')
        return redirect(url_for('list_applications'))

    return render_template(
        'applications/manage_models.html', 
        application=application, 
        all_models=all_models, 
        current_model_ids=current_model_ids,
        default_model_id=default_model_id
    )

@app.route('/app_models')
@login_required
def list_app_models():
    app_models = AppModel.query.all()
    return render_template('app_models/list.html', app_models=app_models)

@app.route('/app_models/delete/<int:id>', methods=['POST'])
@login_required
def delete_app_model(id):
    app_model = AppModel.query.get_or_404(id)
    db.session.delete(app_model)
    db.session.commit()

    flash('应用模型关联删除成功', 'success')
    return redirect(url_for('list_app_models'))

# 添加这个路由来解决错误
@app.route('/app_models/add', methods=['GET', 'POST'])
@login_required
def add_app_model():
    applications = Application.query.all()
    models = AIModel.query.all()

    if request.method == 'POST':
        application_id = request.form['application_id']
        model_id = request.form['model_id']
        is_default = 'is_default' in request.form

        # 检查是否已存在相同的应用模型关联
        if AppModel.query.filter_by(application_id=application_id, model_id=model_id).first():
            flash('该应用已关联此模型', 'danger')
            return redirect(url_for('add_app_model'))

        app_model = AppModel(
            application_id=application_id,
            model_id=model_id,
            is_default=is_default
        )

        # 如果设置为默认，需要取消该应用的其他默认模型
        if is_default:
            existing_defaults = AppModel.query.filter_by(
                application_id=application_id,
                is_default=True
            ).all()
            for default in existing_defaults:
                default.is_default = False

        db.session.add(app_model)
        db.session.commit()

        flash('应用模型关联添加成功', 'success')
        return redirect(url_for('list_app_models'))

    return render_template('app_models/add.html', applications=applications, models=models)

# 添加编辑路由
@app.route('/app_models/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_app_model(id):
    app_model = AppModel.query.get_or_404(id)
    applications = Application.query.all()
    models = AIModel.query.all()

    if request.method == 'POST':
        application_id = request.form['application_id']
        model_id = request.form['model_id']
        is_default = 'is_default' in request.form

        # 检查是否已存在相同的应用模型关联（排除当前记录）
        existing = AppModel.query.filter_by(
            application_id=application_id,
            model_id=model_id
        ).first()
        if existing and existing.id != id:
            flash('该应用已关联此模型', 'danger')
            return redirect(url_for('edit_app_model', id=id))

        app_model.application_id = application_id
        app_model.model_id = model_id

        # 如果设置为默认，需要取消该应用的其他默认模型
        if is_default and not app_model.is_default:
            existing_defaults = AppModel.query.filter_by(
                application_id=application_id,
                is_default=True
            ).all()
            for default in existing_defaults:
                default.is_default = False

        app_model.is_default = is_default
        db.session.commit()

        flash('应用模型关联更新成功', 'success')
        return redirect(url_for('list_app_models'))

    return render_template('app_models/edit.html', app_model=app_model, applications=applications, models=models)



# ======== API接口 ========
@app.route('/api/platforms', methods=['GET'])
@api_enabled_required
def api_platforms():
    platforms = Platform.query.all()
    return jsonify([platform.to_dict() for platform in platforms])

@app.route('/api/models', methods=['GET'])
@api_enabled_required
def api_models():
    models = AIModel.query.all()
    return jsonify([model.to_dict() for model in models])

@app.route('/api/applications', methods=['GET'])
@api_enabled_required
def api_applications():
    applications = Application.query.all()
    return jsonify([app.to_dict() for app in applications])

@app.route('/api/app_models', methods=['GET'])
@api_enabled_required
def api_app_models():
    app_id = request.args.get('app_id')
    if app_id:
        app_models = AppModel.query.filter_by(application_id=app_id).all()
    else:
        app_models = AppModel.query.all()
    return jsonify([am.to_dict() for am in app_models])

@app.route('/api/app/<string:api_key>/models', methods=['GET'])
@api_enabled_required
def api_app_models_by_key(api_key):
    application = Application.query.filter_by(api_key=api_key).first()
    if not application:
        return jsonify({"error": "Invalid API key"}), 401

    app_models = AppModel.query.filter_by(application_id=application.id).all()
    models_data = []

    for am in app_models:
        model_data = am.model.to_dict()
        model_data['is_default'] = am.is_default
        models_data.append(model_data)

    return jsonify(models_data)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
