# models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal

db = SQLAlchemy()

class Platform(db.Model):
    __tablename__ = 'platforms'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    base_url = db.Column(db.String(255), nullable=False)
    api_key = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    models = db.relationship("AIModel", back_populates="platform", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'api_key': self.api_key,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AIModel(db.Model):
    __tablename__ = 'ai_models'

    id = db.Column(db.Integer, primary_key=True)
    display_name = db.Column(db.String(50), unique=True, nullable=False)
    internal_name = db.Column(db.String(100), nullable=False)
    api_endpoint = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 价格字段 (每1000个token的价格，单位为美元)
    input_token_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输入token价格
    output_token_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输出token价格
    input_picture_price = db.Column(db.Numeric(10, 6), default=0.000000)  # 输入图片价格
    is_visible_model = db.Column(db.Boolean, default=False)
    free = db.Column(db.Boolean, default=False)  # 是否免费
    high_price = db.Column(db.Boolean, default=False)  # 是否高价

    platform_id = db.Column(db.Integer, db.ForeignKey('platforms.id'), nullable=False)
    platform = db.relationship("Platform", back_populates="models")

    # 新增与应用关系
    app_models = db.relationship("AppModel", back_populates="model", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'display_name': self.display_name,
            'platform': self.platform.name,
            'internal_name': self.internal_name,
            'is_visible_model': self.is_visible_model,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'input_token_price': float(self.input_token_price),
            'output_token_price': float(self.output_token_price),
            'input_picture_price': float(self.input_picture_price),
            'free': self.free,
            'high_price': self.high_price
        }

class Application(db.Model):
    __tablename__ = 'applications'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text)
    api_key = db.Column(db.String(255), unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 应用与模型的关系
    app_models = db.relationship("AppModel", back_populates="application", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'api_key': self.api_key,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AppModel(db.Model):
    __tablename__ = 'app_models'

    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('applications.id'), nullable=False)
    model_id = db.Column(db.Integer, db.ForeignKey('ai_models.id'), nullable=False)
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    application = db.relationship("Application", back_populates="app_models")
    model = db.relationship("AIModel", back_populates="app_models")

    __table_args__ = (
        db.UniqueConstraint('application_id', 'model_id', name='uix_app_model'),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'application_id': self.application_id,
            'application_name': self.application.name,
            'model_id': self.model_id,
            'model_name': self.model.display_name,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
