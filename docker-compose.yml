version: '3.8'

services:
  model-registry:
    build: .
    container_name: model-registry
    restart: always
    ports:
      - "5000:5000"
    environment:
      - ADMIN_PASSWORD=your_secure_password  # 设置管理员密码
      - API_ENABLED=true                     # 是否启用API功能
      - SQLALCHEMY_DATABASE_URI=mysql+pymysql://root:rw80827@114.96.68.254/model_registry
    volumes:
      - ./:/app
    networks:
      - model-registry-network

networks:
  model-registry-network:
    driver: bridge
