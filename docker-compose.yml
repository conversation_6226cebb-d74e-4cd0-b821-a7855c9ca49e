version: '3.8'

services:
  model-registry:
    build: .
    container_name: model-registry
    restart: always
    ports:
      - "${FLASK_RUN_PORT:-5000}:5000"
    env_file:
      - .env.production  # 生产环境配置文件
    environment:
      # 可以在这里覆盖特定的环境变量
      - FLASK_ENV=production
    volumes:
      - ./logs:/app/logs  # 日志目录
      - ./uploads:/app/uploads  # 上传文件目录
    networks:
      - model-registry-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  model-registry-network:
    driver: bridge
