version: '3.8'

services:
  model-registry-dev:
    build: 
      context: .
      dockerfile: Dockerfile.dev
    container_name: model-registry-dev
    restart: unless-stopped
    ports:
      - "${FLASK_RUN_PORT:-5000}:5000"
    env_file:
      - .env.development  # 开发环境配置文件
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=True
    volumes:
      - ./:/app  # 开发环境挂载整个项目目录以支持热重载
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - model-registry-dev-network
    depends_on:
      - mysql-dev
    command: ["python", "app.py"]

  mysql-dev:
    image: mysql:8.0
    container_name: mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: model_registry_dev
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - model-registry-dev-network

networks:
  model-registry-dev-network:
    driver: bridge

volumes:
  mysql_dev_data:
